import { describe, it, before, after, beforeEach } from 'node:test'
import assert from 'node:assert'
import Context from '../src/context'
import { TENANT, USER, TRAP } from '../src/types'
import { TenantIsolationError } from '@perkd/errors'

describe('Trap Context Functionality', () => {
  let originalContextMode: string | undefined
  let originalDisableAsync: string | undefined

  before(() => {
    originalContextMode = process.env.CONTEXT_MODE
    originalDisableAsync = process.env.DISABLE_ASYNC_CONTEXT
  })

  after(() => {
    if (originalContextMode !== undefined) {
      process.env.CONTEXT_MODE = originalContextMode
    } else {
      delete process.env.CONTEXT_MODE
    }
    
    if (originalDisableAsync !== undefined) {
      process.env.DISABLE_ASYNC_CONTEXT = originalDisableAsync
    } else {
      delete process.env.DISABLE_ASYNC_CONTEXT
    }
  })

  beforeEach(() => {
    // Reset metrics before each test
    Context.metrics.total = 0
    Context.metrics.trapFallbacks = 0
    Context.metrics.strictAccess = 0
    Context.metrics.domainFallbacks = 0
  })

  describe('Trap Context Creation', () => {
    it('should create trap context when no ALS context available', () => {
      process.env.CONTEXT_MODE = 'legacy'
      
      // Access context outside of runInContext - should create trap context
      const context = Context.getCurrentContext()
      
      assert.ok(context, 'Context should be created')
      assert.strictEqual(context[TENANT], TRAP, 'Should use trap tenant')
      assert.strictEqual(context._isTrapContext, true, 'Should be marked as trap context')
      assert.ok(context.requestId, 'Should have request ID')
      assert.ok(context._createdAt, 'Should have creation timestamp')
      assert.strictEqual(typeof context.get, 'function', 'Should have get method')
      assert.strictEqual(typeof context.set, 'function', 'Should have set method')
    })

    it('should throw error in strict mode when no ALS context', () => {
      process.env.CONTEXT_MODE = 'strict'

      assert.throws(() => {
        Context.getCurrentContext()
      }, TenantIsolationError, 'Should throw TenantIsolationError in strict mode')
    })

    it('should track trap context metrics', () => {
      process.env.CONTEXT_MODE = 'legacy'
      
      // Access context multiple times
      Context.getCurrentContext()
      Context.getCurrentContext()
      Context.getCurrentContext()
      
      assert.strictEqual(Context.metrics.total, 3, 'Should track total accesses')
      assert.strictEqual(Context.metrics.trapFallbacks, 3, 'Should track trap fallbacks')
      assert.strictEqual(Context.metrics.strictAccess, 0, 'Should have no strict accesses')
    })
  })

  describe('Trap Context Detection', () => {
    it('should detect trap context by _isTrapContext flag', () => {
      const trapContext = {
        [TENANT]: 'some-tenant',
        _isTrapContext: true
      }
      
      assert.strictEqual(Context.isTrapContext(trapContext), true, 'Should detect trap context by flag')
    })

    it('should detect trap context by TRAP tenant', () => {
      const trapContext = {
        [TENANT]: TRAP
      }
      
      assert.strictEqual(Context.isTrapContext(trapContext), true, 'Should detect trap context by tenant')
    })

    it('should not detect regular context as trap', () => {
      const regularContext = {
        [TENANT]: 'regular-tenant'
      }
      
      assert.strictEqual(Context.isTrapContext(regularContext), false, 'Should not detect regular context as trap')
    })

    it('should check current context when no parameter provided', () => {
      process.env.CONTEXT_MODE = 'legacy'
      
      // Get trap context
      Context.getCurrentContext()
      
      assert.strictEqual(Context.isTrapContext(), true, 'Should detect current context as trap')
    })
  })

  describe('Database Operation Protection', () => {
    it('should prevent runAsTenant with trap tenant and connection manager', async () => {
      const mockConnectionManager = {
        ensureConnection: () => Promise.resolve(),
        startSession: () => Promise.resolve({}),
        withTransaction: () => Promise.resolve()
      } as any

      await assert.rejects(async () => {
        await Context.runAsTenant(TRAP, () => {
          return 'should not execute'
        }, mockConnectionManager)
      }, TenantIsolationError, 'Should prevent database operations with trap tenant')
    })

    it('should allow runAsTenant with trap tenant without connection manager', async () => {
      const result = await Context.runAsTenant(TRAP, () => {
        return 'executed successfully'
      })
      
      assert.strictEqual(result, 'executed successfully', 'Should allow non-database operations with trap tenant')
    })

    it('should allow runAsTenant with regular tenant and connection manager', async () => {
      const mockConnectionManager = {
        ensureConnection: () => Promise.resolve(),
        startSession: () => Promise.resolve({
          hasActiveTransaction: () => false,
          abortTransaction: () => Promise.resolve(),
          endSession: () => Promise.resolve()
        }),
        withTransaction: (tenant: string, fn: () => Promise<void>) => fn(),
        releaseSession: () => Promise.resolve()
      } as any

      const result = await Context.runAsTenant('regular-tenant', () => {
        return 'database operation allowed'
      }, mockConnectionManager)
      
      assert.strictEqual(result, 'database operation allowed', 'Should allow database operations with regular tenant')
    })
  })

  describe('Context Isolation', () => {
    it('should maintain isolation between trap and regular contexts', async () => {
      process.env.CONTEXT_MODE = 'legacy'
      
      // Get trap context outside ALS
      const trapContext = Context.getCurrentContext()
      assert.ok(trapContext, 'Trap context should exist')
      assert.strictEqual(trapContext[TENANT], TRAP, 'Should get trap context')

      // Run in regular context
      await Context.runInContext({
        [TENANT]: 'regular-tenant',
        [USER]: { id: 'user-123', username: 'test-user' }
      }, () => {
        const regularContext = Context.getCurrentContext()
        assert.ok(regularContext, 'Regular context should exist')
        assert.strictEqual(regularContext[TENANT], 'regular-tenant', 'Should get regular context')
        assert.strictEqual(Context.isTrapContext(regularContext), false, 'Should not be trap context')
      })

      // Back to trap context
      const backToTrap = Context.getCurrentContext()
      assert.ok(backToTrap, 'Back to trap context should exist')
      assert.strictEqual(backToTrap[TENANT], TRAP, 'Should return to trap context')
    })

    it('should handle concurrent trap and regular contexts', async () => {
      process.env.CONTEXT_MODE = 'legacy'
      
      const promises = [
        // Regular context operation
        Context.runInContext({
          [TENANT]: 'tenant-a',
          operationId: 'regular'
        }, async () => {
          await new Promise(resolve => setTimeout(resolve, 10))
          return {
            tenant: Context.tenant,
            isTrap: Context.isTrapContext()
          }
        }),
        
        // Operation that will use trap context
        new Promise(resolve => {
          setTimeout(() => {
            resolve({
              tenant: Context.tenant,
              isTrap: Context.isTrapContext()
            })
          }, 5)
        })
      ]
      
      const results = await Promise.all(promises)
      
      assert.strictEqual(results[0].tenant, 'tenant-a', 'Regular context should maintain tenant')
      assert.strictEqual(results[0].isTrap, false, 'Regular context should not be trap')
      assert.strictEqual(results[1].tenant, TRAP, 'Outside context should use trap')
      assert.strictEqual(results[1].isTrap, true, 'Outside context should be trap')
    })
  })

  describe('Performance and Metrics', () => {
    it('should track fallback percentage correctly', () => {
      process.env.CONTEXT_MODE = 'legacy'
      
      // Mix of ALS and trap contexts
      Context.runInContext({ [TENANT]: 'test-tenant' }, () => {
        Context.getCurrentContext() // ALS context
      })
      
      Context.getCurrentContext() // Trap context
      Context.getCurrentContext() // Trap context
      
      assert.strictEqual(Context.metrics.total, 3, 'Should track total accesses')
      assert.strictEqual(Context.metrics.strictAccess, 1, 'Should track ALS accesses')
      assert.strictEqual(Context.metrics.trapFallbacks, 2, 'Should track trap fallbacks')
      
      const fallbackPercentage = Context.metrics.trapFallbacks / Context.metrics.total * 100
      assert.strictEqual(Math.round(fallbackPercentage), 67, 'Should calculate correct fallback percentage')
    })

    it('should not generate expensive stack traces like domain fallback', () => {
      process.env.CONTEXT_MODE = 'legacy'
      
      const startTime = process.hrtime.bigint()
      
      // Create multiple trap contexts
      for (let i = 0; i < 100; i++) {
        Context.getCurrentContext()
      }
      
      const endTime = process.hrtime.bigint()
      const durationMs = Number(endTime - startTime) / 1000000
      
      // Should be much faster than domain fallback with stack trace generation
      assert.ok(durationMs < 100, `Trap context creation should be fast, took ${durationMs}ms`)
    })
  })
})
