import { describe, it, beforeEach } from 'node:test'
import assert from 'node:assert'
import Context from '../src/context'
import { TENANT, USER } from '../src/types'

describe('Enhanced Memory Cleanup', () => {
  beforeEach(() => {
    // Reset metrics before each test
    Context.metrics.total = 0
    Context.metrics.trapFallbacks = 0
    Context.metrics.strictAccess = 0
    Context.metrics.domainFallbacks = 0
  })

  describe('Trap Context Cleanup', () => {
    it('should properly clean up trap contexts', () => {
      process.env.CONTEXT_MODE = 'legacy'
      
      // Create trap context
      const trapContext = Context.getCurrentContext()
      assert.ok(trapContext, 'Trap context should be created')
      assert.strictEqual(trapContext._isTrapContext, true, 'Should be marked as trap context')
      assert.ok(trapContext.requestId, 'Should have request ID')
      assert.ok(trapContext._createdAt, 'Should have creation timestamp')
      assert.strictEqual(typeof trapContext.get, 'function', 'Should have get method')
      assert.strictEqual(typeof trapContext.set, 'function', 'Should have set method')
      
      // Release the context
      Context.releaseContext(trapContext)
      
      // Verify cleanup
      assert.strictEqual(trapContext._released, true, 'Should be marked as released')
      assert.strictEqual(trapContext._isTrapContext, undefined, 'Trap flag should be removed')
      assert.strictEqual(trapContext._createdAt, undefined, 'Creation timestamp should be removed')
      assert.strictEqual(trapContext.get, undefined, 'Get method should be removed')
      assert.strictEqual(trapContext.set, undefined, 'Set method should be removed')
      assert.strictEqual(trapContext.requestId, undefined, 'Request ID should be removed')
      
      // Tenant symbol should be preserved for debugging
      assert.strictEqual(trapContext[TENANT], 'trap', 'Tenant symbol should be preserved')
    })

    it('should clean up nested object references in trap contexts', () => {
      process.env.CONTEXT_MODE = 'legacy'
      
      // Create trap context and add nested objects
      const trapContext = Context.getCurrentContext()
      assert.ok(trapContext, 'Trap context should be created')
      
      // Add nested objects with potential circular references
      trapContext.nestedData = {
        level1: {
          level2: {
            someFunction: () => 'test',
            someData: 'value'
          },
          anotherFunction: () => 'another'
        },
        topLevelFunction: () => 'top'
      }
      
      // Release the context
      Context.releaseContext(trapContext)
      
      // Verify nested cleanup
      assert.strictEqual(trapContext.nestedData, undefined, 'Nested data should be removed')
      assert.strictEqual(trapContext._released, true, 'Should be marked as released')
    })
  })

  describe('Regular Context Cleanup', () => {
    it('should properly clean up regular tenant contexts', async () => {
      const testContext: any = {
        [TENANT]: 'test-tenant',
        [USER]: { id: 'user-123', username: 'testuser' },
        requestId: 'test-request-123',
        customData: {
          nested: {
            value: 'test',
            func: () => 'nested function'
          }
        }
      }

      testContext.get = (key: string): any => testContext[key]
      testContext.set = (key: string, value: any): void => { testContext[key] = value }
      
      // Verify initial state
      assert.strictEqual(testContext[TENANT], 'test-tenant', 'Should have tenant')
      assert.ok(testContext[USER], 'Should have user')
      assert.strictEqual(typeof testContext.get, 'function', 'Should have get method')
      assert.strictEqual(typeof testContext.set, 'function', 'Should have set method')
      assert.ok(testContext.customData.nested.func, 'Should have nested function')
      
      // Release the context
      Context.releaseContext(testContext)
      
      // Verify cleanup - should only remove get/set functions, preserve user data
      assert.strictEqual(testContext._released, true, 'Should be marked as released')
      assert.ok(testContext[USER], 'User should be preserved (not removed)')
      assert.strictEqual(testContext.requestId, 'test-request-123', 'Request ID should be preserved')
      assert.ok(testContext.customData, 'Custom data should be preserved')
      assert.strictEqual(testContext.get, undefined, 'Get method should be removed')
      assert.strictEqual(testContext.set, undefined, 'Set method should be removed')

      // Tenant symbol should be preserved
      assert.strictEqual(testContext[TENANT], 'test-tenant', 'Tenant symbol should be preserved')
    })

    it('should handle context cleanup within runInContext', async () => {
      let contextBeforeCleanup: any
      let contextAfterCleanup: any
      
      await Context.runInContext({
        [TENANT]: 'cleanup-test-tenant',
        testData: 'should be cleaned',
        nestedObject: {
          func: () => 'nested',
          data: 'nested data'
        }
      }, () => {
        contextBeforeCleanup = { ...Context.getCurrentContext() }
        // Context will be cleaned up automatically in finally block
      })
      
      // After runInContext, try to access the context (should get trap context)
      contextAfterCleanup = Context.getCurrentContext()
      
      // Verify the context was properly cleaned up
      assert.strictEqual(contextBeforeCleanup[TENANT], 'cleanup-test-tenant', 'Original context should have correct tenant')
      assert.strictEqual(contextBeforeCleanup.testData, 'should be cleaned', 'Original context should have test data')
      
      // After cleanup, we should get a new trap context
      assert.strictEqual(contextAfterCleanup[TENANT], 'trap', 'Should get trap context after cleanup')
      assert.strictEqual(Context.isTrapContext(contextAfterCleanup), true, 'Should be trap context')
    })
  })

  describe('Memory Leak Prevention', () => {
    it('should prevent memory leaks from function closures', () => {
      process.env.CONTEXT_MODE = 'legacy'
      
      // Create context with function that captures 'this'
      const trapContext = Context.getCurrentContext()
      assert.ok(trapContext, 'Trap context should be created')
      
      // Add functions that could create memory leaks
      trapContext.leakyFunction = function() {
        return this.someProperty // Captures 'this'
      }
      
      trapContext.anotherLeakyFunction = () => {
        return trapContext.someProperty // Captures trapContext
      }
      
      // Verify functions exist
      assert.strictEqual(typeof trapContext.leakyFunction, 'function', 'Leaky function should exist')
      assert.strictEqual(typeof trapContext.anotherLeakyFunction, 'function', 'Another leaky function should exist')
      
      // Release context
      Context.releaseContext(trapContext)
      
      // Verify functions are removed
      assert.strictEqual(trapContext.leakyFunction, undefined, 'Leaky function should be removed')
      assert.strictEqual(trapContext.anotherLeakyFunction, undefined, 'Another leaky function should be removed')
      assert.strictEqual(trapContext._released, true, 'Should be marked as released')
    })

    it('should handle null and undefined contexts gracefully', () => {
      // Should not throw errors
      assert.doesNotThrow(() => {
        Context.releaseContext(null as any)
      }, 'Should handle null context')
      
      assert.doesNotThrow(() => {
        Context.releaseContext(undefined as any)
      }, 'Should handle undefined context')
    })

    it('should handle contexts with circular references', () => {
      const circularContext = {
        [TENANT]: 'circular-tenant',
        requestId: 'circular-test'
      } as any
      
      // Create circular reference
      circularContext.self = circularContext
      circularContext.nested = {
        parent: circularContext,
        func: () => circularContext
      }
      
      // Should not throw error or cause infinite loop
      assert.doesNotThrow(() => {
        Context.releaseContext(circularContext)
      }, 'Should handle circular references')

      // Verify cleanup - circular references should be cleaned but main properties preserved
      assert.strictEqual(circularContext._released, true, 'Should be marked as released')
      // Note: Our cleanup strategy preserves main properties but cleans nested circular refs
      assert.strictEqual(circularContext.requestId, 'circular-test', 'Request ID should be preserved')
      assert.strictEqual(circularContext[TENANT], 'circular-tenant', 'Tenant should be preserved')
    })
  })

  describe('Performance and Debugging', () => {
    it('should provide debugging information for released contexts', () => {
      process.env.CONTEXT_MODE = 'legacy'
      
      const trapContext = Context.getCurrentContext()
      const originalTenant = trapContext?.[TENANT]
      
      Context.releaseContext(trapContext!)
      
      // Should preserve tenant for debugging
      assert.strictEqual(trapContext![TENANT], originalTenant, 'Tenant should be preserved for debugging')
      assert.strictEqual(trapContext!._released, true, 'Should have release marker')
      
      // Should be configurable: false to prevent accidental modification
      const descriptor = Object.getOwnPropertyDescriptor(trapContext!, '_released')
      assert.strictEqual(descriptor?.configurable, false, 'Release marker should not be configurable')
    })

    it('should handle cleanup errors gracefully', () => {
      const problematicContext = {
        [TENANT]: 'error-tenant',
        get problematicProperty() {
          throw new Error('Property access error')
        }
      } as any
      
      // Should not throw error even if property access fails
      assert.doesNotThrow(() => {
        Context.releaseContext(problematicContext)
      }, 'Should handle cleanup errors gracefully')
      
      // Should still mark as released
      assert.strictEqual(problematicContext._released, true, 'Should still be marked as released')
    })
  })
})
