import { describe, it, before } from 'node:test'
import assert from 'node:assert'
import { Security } from '@perkd/utils'
import Context from '../src/context'
import { TENANT } from '../src/types'

describe('JWT Context Handling', () => {
  before(() => {
    process.env.CONTEXT_MODE = 'strict'
    process.env.DISABLE_ASYNC_CONTEXT = 'false'
  })

  const secret = 'test-secret'
  const testPayload = {
    tenant: { code: 'jwt-tenant', timezone: 'UTC' },
    user: { id: 'jwt-user' }
  }

  it('should generate and validate access token', async () => {
    await Context.runInContext({
      [TENANT]: 'jwt-tenant',
      user: { id: 'jwt-userId', username: 'jwt-username' }
    }, async () => {
      const token = Context.generateAccessToken()
      assert.ok(token, 'Should generate valid token')
    })
  })

  it('should handle expired tokens', async () => {
    const jwt = new Security.Jwt(secret)
    const expiredPayload = { ...testPayload, exp: Math.floor(Date.now() / 1000) - 3600 }
    const token = jwt.encode(expiredPayload)

    // Run in context to avoid strict mode error
    await Context.runInContext({
      [TENANT]: 'test-tenant'
    }, () => {
      const result = Context.setWithToken(token, secret)
      assert(result instanceof Error, 'Should detect expired token')
    })
  })
})