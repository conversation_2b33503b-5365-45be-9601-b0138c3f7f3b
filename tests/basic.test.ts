import { describe, it, before, after, mock } from 'node:test'
import assert from 'node:assert'
import Context from '../src/context'
import { CRM_User, TENANT, USER } from '../src/types'

describe('MultitenantContext', () => {
  before(() => {
    process.env.CONTEXT_MODE = 'strict'
    process.env.DISABLE_ASYNC_CONTEXT = 'false'
  })

  it('should create and retrieve basic context', () => {
    const testContext = Context.createContext({
      [TENANT]: 'test-tenant',
      [USER]: { id: 'test-user', username: 'test-username' }
    })

    assert.strictEqual(testContext[TENANT], 'test-tenant')
    assert.strictEqual((testContext[USER] as CRM_User).id, 'test-user')
  })

  it('should maintain context through async operations', async () => {
    await Context.runInContext({ 
      [TENANT]: 'async-tenant',
      [USER]: { id: 'async-user', username: 'async-user' }
    }, async () => {
      assert.strictEqual(Context.tenant, 'async-tenant')
      
      await new Promise(resolve => setTimeout(resolve, 10))
      assert.strictEqual(Context.tenant, 'async-tenant')
    })
  })

  it('should use trap context when no ALS context available', () => {
    const originalMode = process.env.CONTEXT_MODE
    process.env.CONTEXT_MODE = 'legacy'

    // Access context outside of runInContext - should get trap context
    const tenant = Context.tenant
    assert.strictEqual(tenant, 'trap', 'Should use trap tenant when no context available')

    process.env.CONTEXT_MODE = originalMode
  })
}) 