# @perkd/multitenant-context

A drop-in replacement for loopback-context with enhanced multitenant support for Node.js applications.

[![Version](https://img.shields.io/badge/version-0.7.0-blue.svg)](https://github.com/perkd/multitenant-context)
[![Node](https://img.shields.io/badge/node->=20-green.svg)](https://nodejs.org/)

## Overview

`multitenant-context` provides a robust solution for managing context data across asynchronous operations in Node.js applications, with a focus on multitenancy. It maintains tenant isolation while preserving context through async operations, making it ideal for SaaS applications and microservices architectures.

This library is a modern replacement for the legacy `loopback-context` module, built with TypeScript and leveraging Node.js AsyncLocalStorage for reliable context propagation.

### Architecture

```mermaid
flowchart TD
    subgraph RP ["Request Processing"]
        A[API Request] --> B[Context Middleware]
        B --> C{JWT Token?}
        C -->|Yes| D[Extract Token Data]
        C -->|No| E[Create Empty Context]
        D --> F[Create Context]
        E --> F
        F --> G[AsyncLocalStorage]
    end

    subgraph TAC ["Tenant A Context"]
        G --> H1[Business Logic]
        H1 --> I1[Database Operations]
        H1 --> J1[External API Calls]
        H1 --> K1[Async Operations]
    end

    subgraph TBC ["Tenant B Context"]
        G --> H2[Business Logic]
        H2 --> I2[Database Operations]
        H2 --> J2[External API Calls]
        H2 --> K2[Async Operations]
    end

    %% Styling for better contrast
    classDef tenantA fill:#8B0000,stroke:#333,stroke-width:2px,color:#fff
    classDef tenantB fill:#000080,stroke:#333,stroke-width:2px,color:#fff
    classDef processing fill:#2F4F4F,stroke:#333,stroke-width:2px,color:#fff

    class H1,I1,J1,K1 tenantA
    class H2,I2,J2,K2 tenantB
    class A,B,C,D,E,F,G processing
```

### Context Flow

```mermaid
sequenceDiagram
    participant Client
    participant Middleware
    participant Context
    participant AsyncOps as Async Operations
    participant DB as Database

    Client->>Middleware: Request with tenant info
    Middleware->>Context: Create context with tenant
    Context->>Context: Store in AsyncLocalStorage
    Middleware->>AsyncOps: Execute business logic
    AsyncOps->>Context: Access tenant context
    AsyncOps->>DB: Perform DB operations
    DB->>AsyncOps: Return results
    AsyncOps->>Client: Response with tenant data

    Note over Context,AsyncOps: Context is preserved<br>across async boundaries
```

## Features

- **Tenant Isolation**: Strict tenant context boundaries to prevent data leakage between tenants.
- **Async Context Preservation**: Maintains context through async operations, promises, and callbacks using `AsyncLocalStorage`.
- **Advanced Security**: Trap context fallback mechanism prevents cross-tenant contamination.
- **JWT Integration**: Built-in JWT token generation and validation for authentication.
- **TypeScript Support**: Full TypeScript definitions for improved developer experience.
- **Legacy Compatibility**: Provides a compatibility layer for projects migrating from `loopback-context`.
- **Connection Management**: Integration with database connection managers for tenant-specific transactions.
- **Performance Monitoring**: Built-in tracking of context usage patterns and fallback metrics.

## Installation

```bash
yarn add @perkd/multitenant-context
```

## Security Enhancements

### Trap Context Fallback
This library includes advanced security features to prevent cross-tenant data contamination:

- **Safe Fallback Mechanism**: Replaces dangerous empty object returns with secure "trap" contexts
- **Database Operation Protection**: Prevents trap contexts from accessing database operations
- **Security Logging**: Comprehensive tracking of fallback usage with caller information
- **Zero Cross-Tenant Risk**: Eliminates the possibility of accessing data from other tenants

When context is accessed outside of proper AsyncLocalStorage boundaries, the library creates a safe "trap" context instead of returning an empty object that could lead to data leakage.

### Context Behavior (Precise)

#### Within AsyncLocalStorage Boundaries
- **ALS Success Rate: 100%** - Context always available from AsyncLocalStorage
- **Fallback Rate: 0%** - No fallback mechanism needed

#### Outside AsyncLocalStorage Boundaries
- **Trap Context Rate: 100%** - Safe fallback context always created
- **Legacy Fallback Rate: 0%** - Dangerous domain fallback completely eliminated
- **Cross-tenant Risk: 0%** - Trap contexts prevent data contamination

#### Strict Mode
- **Error Rate: 100%** - Throws TenantIsolationError immediately
- **Fallback Rate: 0%** - No fallback of any kind permitted

## Performance

- **AsyncLocalStorage Optimization**: 100% success rate within proper context boundaries
- **Trap Context Efficiency**: 30x performance improvement over legacy domain fallback
- **Memory Management**: Enhanced cleanup prevents memory leaks with circular reference protection
- **Zero Overhead**: No performance impact when used within proper context boundaries

## Basic Usage

```typescript
import { Context, TENANT, USER } from '@perkd/multitenant-context';

// Run code within a specific tenant context
await Context.runInContext({
  [TENANT]: 'my-tenant',
  [USER]: { id: 'user-123', username: 'john.doe' }
}, async () => {
  // Context is available here and in all async operations
  console.log(Context.tenant); // 'my-tenant'

  await someAsyncOperation();
  
  // Context is still preserved here
  console.log(Context.tenant); // 'my-tenant'
});
```

## Advanced Usage

### Tenant Isolation with Database Transactions

```typescript
import { Context } from '@perkd/multitenant-context';
import { connectionManager } from './your-db-connection'; // Assuming you have a connection manager

// Run operations in a specific tenant with database transaction support
await Context.runAsTenant('tenant-a', async () => {
  // Operations here run within tenant-a's context and transaction
  const data = await fetchSomeTenantData();
  await updateTenantData(data);
}, connectionManager);
```

### JWT Token Management

```typescript
import { Context } from '@perkd/multitenant-context';

// Generate a JWT token with the current context
const token = Context.generateAccessToken({ custom: 'payload' });

// Set context from a JWT token
const result = Context.setWithToken(token, 'your-secret-key');
if (result instanceof Error) {
  console.error('Token validation failed:', result.message);
} else {
  console.log('Context set from token. Tenant:', Context.tenant);
}
```

### Running with Elevated Privileges

```typescript
import { Context } from '@perkd/multitenant-context';

// Execute a function with elevated privileges
await Context.runWithPrivilege(
  { id: 'admin-user', username: 'admin' },
  'admin',
  async () => {
    // Operations here run with admin privileges
    await performAdminOperation();
  },
  'target-tenant' // Optional: specify a tenant for the operation
);
```

## Tenant Isolation Concept

The core concept of this library is maintaining strict tenant isolation throughout asynchronous operations.

```mermaid
graph TD
    ALS[AsyncLocalStorage]

    subgraph TAC ["Tenant A Context"]
        A1[API Endpoint]
        B1[Business Logic]
        B1 --> D1[External API Call]
        B1 --> E1[Async Operation]
        B1 --> C1[Database Query]
    end

    subgraph TBC ["Tenant B Context"]
        A2[API Endpoint]
        B2[Business Logic]
        B2 --> C2[Database Query]
        B2 --> E2[Async Operation]
        B2 --> D2[External API Call]
    end

    subgraph DB ["Database"]
        G1[Tenant A Data]
        G2[Tenant B Data]
    end

    A1 --> ALS
    A2 --> ALS
    ALS --> B1
    ALS --> B2
    C1 --> G1
    C2 --> G2

    %% Styling for better contrast with dark backgrounds
    classDef tenantA fill:#8B0000,stroke:#333,stroke-width:2px,color:#fff
    classDef tenantB fill:#000080,stroke:#333,stroke-width:2px,color:#fff
    classDef database fill:#2F4F4F,stroke:#333,stroke-width:2px,color:#fff
    classDef storage fill:#4B0082,stroke:#333,stroke-width:2px,color:#fff

    class A1,B1,C1,D1,E1,G1 tenantA
    class A2,B2,C2,D2,E2,G2 tenantB
    class ALS storage
```

## Configuration

The library can be configured using environment variables:

- `CONTEXT_MODE`: Set to `strict` (default) to enforce tenant isolation or `legacy` for backward compatibility. In strict mode, accessing context outside AsyncLocalStorage boundaries throws an error instead of creating trap contexts.
- `DISABLE_ASYNC_CONTEXT`: Set to `true` to disable `AsyncLocalStorage` and fall back to trap context mechanism for all operations.
- `PERKD_SECRET_KEY`: Default secret key for JWT operations. This can be overridden in method calls.

## API Reference

### Context Class

The main class providing context management functionality.

#### Static Methods

- `withContext(context, fn)`: Legacy compatibility method for running a function within a given context.

#### Instance Methods & Properties

- `createContext(initialValue?)`: Creates a new context object.
- `releaseContext(context)`: Releases a context to prevent memory leaks.
- `getCurrentContext()`: Gets the current context object from `AsyncLocalStorage` or creates a safe trap context fallback.
- `isTrapContext(context?)`: Checks if a context is a trap context (fallback).
- `get(key)`: Gets a value from the current context by key.
- `set(key, value)`: Sets a value in the current context.
- `runInContext(context, fn)`: Runs a function within the specified context.
- `runAsTenant(tenant, fn, connectionManager?, options?)`: Runs a function within a specific tenant context.
- `runWithPrivilege(user, role, fn, tenantCode?)`: Runs a function with elevated privileges.
- `setValues(tenant, user?, timezone?, origin?)`: Sets multiple context values.
- `setWithToken(accessToken, secretKey, options?)`: Sets the context from a JWT.
- `generateAccessToken(payload?, secret?)`: Generates a JWT from the current context.
- `bindEmitter(emitter)`: Binds an event emitter to the current context.
- `metrics`: Access to context usage metrics including trap fallback rates.

#### Accessors

- `tenant`: Gets or sets the current tenant code.
- `user`: Gets or sets the current user object.
- `accessToken`: Gets or sets the current access token.
- `timezone`: Gets or sets the current timezone.
- `origin`: Gets or sets the request origin.
- `idempotencyKey`: Gets or sets the idempotency key.
- `installation`: Gets or sets the installation information.
- `cardProfile`: Gets or sets the card profile.
- `location`: Gets or sets the location information.
- `language`: Gets or sets the language preference.
- `appContext`: A getter that returns a composite object with staff, user, card, location, and installation details.

## Types

The library exports several type definitions for robust development:

- `TENANT`, `USER`, `ACCESS_TOKEN`, etc.: `Symbol` constants for context keys.
- `User`: Union type for `CRM_User` or `Wallet_User`.
- `Installation`: Interface for installation information.
- `Location`: Interface for location information.
- `Language`: Type alias for language preference.
- `Card`: Interface for card profile.
- `JwtPayload`: Type for JWT payload.

## Troubleshooting

### Context Loss in Async Operations

If you're experiencing context loss:

1.  Ensure you wrap your async operations with `runInContext` or `runAsTenant`.
2.  Check that `DISABLE_ASYNC_CONTEXT` is not set to `true`.
3.  Verify you are using a Node.js version that supports `AsyncLocalStorage` (>= v16.x).
4.  Look for trap context warnings in your logs, which indicate parts of your code are accessing context outside AsyncLocalStorage boundaries.

```typescript
// Incorrect: context may be lost
Context.tenant = 'my-tenant';
setTimeout(() => {
  console.log(Context.tenant); // May be undefined
}, 100);

// Correct: context is preserved
await Context.runInContext({ [TENANT]: 'my-tenant' }, async () => {
  setTimeout(() => {
    console.log(Context.tenant); // Will be 'my-tenant'
  }, 100);
});
```

### JWT Token Validation Errors

If you encounter JWT validation issues:

1.  Check that the token has not expired.
2.  Verify you are using the correct secret key for validation.
3.  Ensure the token payload has the expected structure.

### Trap Context Warnings

If you see trap context warnings like:
`Trap context fallback (1): 25% [someFunction at file.js:123:45]`

This indicates context is being accessed outside of an `AsyncLocalStorage` context. This is **safe** but may indicate suboptimal usage patterns:

1.  **Safe Operation**: Trap contexts prevent data contamination and provide secure fallback behavior
2.  **Performance**: Consider wrapping the code with `runInContext` or `runAsTenant` for optimal performance
3.  **Database Operations**: Trap contexts cannot perform database operations for security reasons
4.  **Monitoring**: High trap context usage may indicate areas for code optimization

**Note**: Unlike legacy domain fallback, trap context fallback is secure and will not cause data leakage.

## Migration from `loopback-context`

1.  **Replace Imports**:
    ```typescript
    // Before
    import { LoopBackContext } from 'loopback-context';

    // After
    import { Context } from '@perkd/multitenant-context';
    ```

2.  **Update Method Calls**:
    ```typescript
    // Before
    const ctx = LoopBackContext.getCurrentContext();

    // After
    const ctx = Context.getCurrentContext();
    ```

3.  Set `CONTEXT_MODE=legacy` in your environment for initial backward compatibility.
4.  Gradually refactor your code to use `runInContext` and `runAsTenant` instead of domain-based approaches.
5.  Once migration is complete, set `CONTEXT_MODE=strict` to leverage full tenant isolation.

### New in v0.6.3+: Trap Context Security

If migrating from earlier versions:

1.  **No Breaking Changes**: All existing code continues to work
2.  **Enhanced Security**: Automatic protection against cross-tenant contamination
3.  **New Logging**: Monitor trap context usage to optimize performance
4.  **Database Safety**: Trap contexts automatically prevent unsafe database operations

## License

This project is proprietary and confidential. Unauthorized copying, transferring, or reproduction of the contents of this project, via any medium, is strictly prohibited.

---

© 2025 Perkd. All rights reserved.
