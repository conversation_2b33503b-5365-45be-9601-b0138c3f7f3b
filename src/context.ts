import { AsyncLocalStorage } from 'node:async_hooks'
import domain from 'node:domain'
import { Session, ConnectionManager } from '@crm/loopback'
import { User as Wallet_User } from '@perkd/wallet'
import { Security, shortId } from '@perkd/utils'
import { TenantIsolationError } from '@perkd/errors'

import { ACCESS_TOKEN, TENANT, USER, TIMEZONE, ORIGIN, TRAP, INSTALL, LANGUAGE, LOCATION, CARD, CRM_User, IDEMPOTENCY_KEY } from './types'
import { User, Installation, Location, Language, Card, Payload, JwtPayload } from './types'

// environment variables
const {
	DISABLE_ASYNC_CONTEXT,
	PERKD_SECRET_KEY = 'perkd-secret-key'
} = process.env

declare module 'node:domain' {
	interface Domain {
		_loopbackContext?: ContextType
	}
}

declare global {
	namespace NodeJS {
		interface Process {
			domain?: domain.Domain
		}
	}
}

interface ContextType {
	[key: string]: any
	[TENANT]?: string
	[ACCESS_TOKEN]?: string
	[USER]?: User
	[TIMEZONE]?: string
	[ORIGIN]?: string
	[IDEMPOTENCY_KEY]?: string
	[INSTALL]?: Installation
	[CARD]?: Card
	[LOCATION]?: Location
	[LANGUAGE]?: Language
	tenantConfig?: Record<string, any>
	_domain?: domain.Domain
	get?: (key: string) => any
	set?: (key: string, value: any) => any
	_contextInstance?: MultitenantContext
	_isEnhanced?: boolean
	_pooled?: boolean
}

interface Metrics {
	domainFallbacks: number		// Legacy - will be deprecated
	trapFallbacks: number		// NEW: Track trap context usage
	strictAccess: number
	total: number
	circularRefsFixed: number	// Track circular reference fixes
	contextsPooled: number		// Track context reuse
}

interface SessionTracker {
	tenant: string
	session: Session
	connectionManager: ConnectionManager
	createdAt: number
}

const { Jwt } = Security,
	ENGLISH = 'en',
	EXPIRY = 'exp'		// jwt

class MultitenantContext {
	private static _instance: MultitenantContext

	private als: AsyncLocalStorage<ContextType>
	private activeDomains: WeakMap<domain.Domain, boolean>
	public metrics: Metrics
	public enabled: boolean
	private _activeContextsRegistry?: Set<ContextType>
	private _contextPool: ContextType[] = []
	private _maxPoolSize = 50
	private _activeSessions = new Map<string, SessionTracker>()

	static withContext(context: ContextType, fn: () => any): any {
		const instance = new MultitenantContext()
		return instance.runInContext(context, fn)
	}

	constructor() {
		if (MultitenantContext._instance) {
			return MultitenantContext._instance
		}

		this.als = new AsyncLocalStorage<ContextType>()
		this.activeDomains = new WeakMap()
		this.metrics = {
			domainFallbacks: 0,
			trapFallbacks: 0,
			strictAccess: 0,
			total: 0,
			circularRefsFixed: 0,
			contextsPooled: 0
		}
		this.enabled = DISABLE_ASYNC_CONTEXT !== 'true'

		MultitenantContext._instance = Object.freeze(this) as unknown as MultitenantContext
	}

	// ---  loopback-context compatibility

	createContext(initialValue: ContextType = {}): ContextType {
		const { requestId = this.generateRequestId() } = initialValue

		// Try to get a context from the pool first
		let context = this.getPooledContext()
		
		if (context) {
			// Reset pooled context with new values
			Object.keys(context).forEach(key => {
				if (key !== '_pooled') {
					delete context[key]
				}
			})
			Object.assign(context, initialValue)
			context.requestId = requestId
			this.metrics.contextsPooled++
		} else {
			// Create new context
			context = {
				...initialValue,
				requestId
			}
		}

		// Add safe accessor methods that don't create circular references
		this.addSafeAccessors(context)

		// Add domain for legacy compatibility (will be deprecated)
		if (process.env.CONTEXT_MODE !== 'strict') {
			context._domain = this._createDomain(context)
		}

		return context
	}

	/**
	 * Add safe accessor methods that don't create circular references
	 */
	private addSafeAccessors(context: ContextType): void {
		// Create bound methods that don't reference 'this' directly
		const contextInstance = this
		
		// Safe get method - captures context at creation time
		Object.defineProperty(context, 'get', {
			value: function(key: string): any {
				return context[key]
			},
			writable: false,
			enumerable: false,
			configurable: true
		})

		// Safe set method - captures context at creation time  
		Object.defineProperty(context, 'set', {
			value: function(key: string, value: any): any {
				context[key] = value
				return value
			},
			writable: false,
			enumerable: false,
			configurable: true
		})

		// Mark as having safe accessors
		context._contextInstance = contextInstance
	}

	/**
	 * Get a context from the pool
	 */
	private getPooledContext(): ContextType | null {
		if (this._contextPool.length > 0) {
			const context = this._contextPool.pop()!
			context._pooled = false
			return context
		}
		return null
	}

	/**
	 * Return a context to the pool
	 */
	private returnToPool(context: ContextType): void {
		if (this._contextPool.length < this._maxPoolSize && !context._isEnhanced) {
			// Clean the context before pooling
			this.cleanContextForPooling(context)
			context._pooled = true
			this._contextPool.push(context)
		}
	}

	/**
	 * Clean a context for pooling (remove user data but keep structure)
	 */
	private cleanContextForPooling(context: ContextType): void {
		const keysToKeep = new Set(['get', 'set', '_contextInstance', '_pooled'])
		
		Object.keys(context).forEach(key => {
			if (!keysToKeep.has(key)) {
				delete context[key]
			}
		})
	}

	releaseContext(context: ContextType): void {
		if (!context) return

		const contextId = context.requestId || 'unknown'
		const isTrap = this.isTrapContext(context)

		try {
			// Track circular reference fixes
			let circularRefsFixed = 0

			// Legacy domain cleanup (will be removed after domain deprecation)
			if (context._domain) {
				context._domain.removeAllListeners()
				if (context._domain._loopbackContext === context) {
					delete context._domain._loopbackContext
					circularRefsFixed++
				}
				this.activeDomains.delete(context._domain)
				delete context._domain
			}

			// Break circular references in get/set functions
			if (context.get && typeof context.get === 'function') {
				delete context.get
				circularRefsFixed++
			}
			if (context.set && typeof context.set === 'function') {
				delete context.set
				circularRefsFixed++
			}

			// Remove context instance reference
			if (context._contextInstance) {
				delete context._contextInstance
				circularRefsFixed++
			}

			// Clean up trap context specific properties
			if (isTrap) {
				delete context._isTrapContext
				delete context._createdAt
				// For trap contexts, clean up all properties except tenant (they're all generated)
				Object.keys(context).forEach(key => {
					if (key !== TENANT.toString() && key !== '_released') {
						try {
							delete context[key]
						} catch (err) {
							console.debug(`Failed to delete trap property ${key}:`, err instanceof Error ? err.message : String(err))
						}
					}
				})
			} else {
				// For regular contexts, clean nested objects with circular reference protection
				const visited = new WeakSet()
				Object.keys(context).forEach(key => {
					try {
						if (context[key] && typeof context[key] === 'object' && key !== TENANT.toString()) {
							this.cleanNestedReferences(context[key], visited)
						}
					} catch (err) {
						console.debug(`Failed to clean nested property ${key}:`, err instanceof Error ? err.message : String(err))
					}
				})

				// Try to return non-enhanced contexts to pool
				if (!context._isEnhanced) {
					this.returnToPool(context)
				}
			}

			// Update metrics
			this.metrics.circularRefsFixed += circularRefsFixed

			console.debug(`Released ${isTrap ? 'trap' : 'tenant'} context ${contextId} (fixed ${circularRefsFixed} circular refs)`)
		} catch (err) {
			console.error('Context cleanup error:', err)
		} finally {
			// Always mark as released, even if cleanup fails
			try {
				Object.defineProperty(context, '_released', {
					value: true,
					configurable: false
				})
			} catch (err) {
				// Fallback if defineProperty fails
				context._released = true
			}
		}
	}

	private cleanNestedReferences(obj: any, visited: WeakSet<object> = new WeakSet()): void {
		if (!obj || typeof obj !== 'object' || visited.has(obj)) return

		visited.add(obj)

		Object.keys(obj).forEach(key => {
			try {
				if (typeof obj[key] === 'function') {
					delete obj[key]
					this.metrics.circularRefsFixed++
				} else if (obj[key] && typeof obj[key] === 'object' && !visited.has(obj[key])) {
					this.cleanNestedReferences(obj[key], visited)
				}
			} catch (err) {
				// Continue cleanup even if individual property fails
				console.debug(`Failed to clean nested property ${key}:`, err instanceof Error ? err.message : String(err))
			}
		})
	}

	getCurrentContext(): ContextType | undefined {
		const { metrics } = this
		const ctx = this.als.getStore()

		metrics.total++

		if (ctx) {
			metrics.strictAccess++
			return ctx
		}

		// Replace dangerous domain fallback with safe trap context
		if (process.env.CONTEXT_MODE === 'strict') {
			throw new TenantIsolationError('No tenant context in strict mode')
		}

		metrics.trapFallbacks++
		console.warn(`Trap context fallback (${metrics.trapFallbacks}): ${metrics.trapFallbacks / metrics.total * 100}%`)

		return this.createTrapContext()
	}

	get(key: string): any {
		return this.context?.[key]
	}

	set(key: string, value: any): any {
		if (this.context) {
			this.context[key] = value
			return value
		}
	}

	// ---  accessors

	private get context(): ContextType | undefined {
		return this.getCurrentContext()
	}

	get tenant(): string {
		return this.context?.[TENANT] || TRAP
	}

	set tenant(code: string) {
		if (this.context) {
			this.context[TENANT] = code.toLowerCase()
		}
	}

	get accessToken(): string {
		return this.context?.[ACCESS_TOKEN] || ''
	}

	set accessToken(token: string) {
		if (this.context) {
			this.context[ACCESS_TOKEN] = token
		}
	}

	get timezone(): string | undefined {
		return this.context?.[TIMEZONE]
	}

	set timezone(timezone: string | undefined) {
		if (this.context) {
			this.context[TIMEZONE] = timezone
		}
	}

	get user(): User | undefined {
		return this.context?.[USER]
	}

	set user(user: User | undefined) {
		if (this.context) {
			this.context[USER] = user
		}
	}

	get origin(): string | undefined {
		return this.context?.[ORIGIN]
	}

	set origin(origin: string) {
		if (this.context) {
			this.context[ORIGIN] = origin
		}
	}

	get idempotencyKey() {
		return this.get(IDEMPOTENCY_KEY) || shortId()
	}

	set idempotencyKey(value: string) {
		if (this.context) {
			this.context[IDEMPOTENCY_KEY] = value
		}
	}

	//  Perkd App specific

	get appContext() {
		const { location: loc, user, cardProfile: card, installation } = this,
			{ spot } = loc ?? {},
			{ id: userId, staffId, username: userName } = <CRM_User>user ?? {},
			staff = staffId ? { id: staffId, userId, userName } : undefined,
			{ id, type, name } = spot ?? {},
			location = { id, type, name }

		return { staff, user, card, location, installation }
	}

	get accountId(): string | null {
		const { accountId = null } = this.user as Wallet_User ?? {}
		return accountId
	}

	/**
	 * Get installation & user from Perkd App
	 * @return	{Object} { ...install, ...user }
	 * 1. install - decoded JWT perkd-install header. (see installation/docs/perkd-install.json)
	 * 2. user - user object in decoded JWT Authorization header. (see installation/docs/Authorization.json)
	 */
	get installation(): Installation | {} {
		const { user } = this,
			install = this.get(INSTALL),
			{ id: userId } = user as CRM_User ?? {},
			{ personId, accountId } = user as Wallet_User ?? {}

		if (!install) return {}
		if (!install.id) {
			const err = new Error('header_missing_install')
			console.error('getInstall', { install, userId, personId, accountId, err })
		}

		// CRM: inject userId (personId & accountId N.A) (placeId already in install)
		// Perkd: inject personId, accountId (userId N.A)
		return { ...install, userId, personId, accountId }
	}

	set installation(installation: Installation) {
		if (this.context) {
			this.context[INSTALL] = installation
		}
	}

	get cardProfile(): Card {
		return this.get(CARD) || {}
	}

	set cardProfile(card: Card) {
		if (this.context) {
			this.context[CARD] = card
		}
	}

	get location(): Location {
		return this.get(LOCATION) || {}
	}

	set location(location: Location) {
		if (this.context) {
			this.context[LOCATION] = location
		}
	}

	get language(): Language {
		return this.get(LANGUAGE) || ENGLISH
	}

	set language(language: Language) {
		if (this.context) {
			this.context[LANGUAGE] = language
		}
	}

	// ---  Trap Context Utilities

	/**
	 * Check if a context is a trap context
	 * @param context - Context to check (defaults to current context)
	 * @returns true if context is a trap context
	 */
	isTrapContext(context?: ContextType): boolean {
		const ctx = context || this.context
		return ctx?._isTrapContext === true || ctx?.[TENANT] === TRAP
	}

	/**
	 * Validate tenant for database operations
	 * @param tenantCode - Tenant code to validate
	 * @throws TenantIsolationError if tenant is trap
	 */
	private validateTenantForDatabaseOps(tenantCode: string): void {
		if (tenantCode === TRAP) {
			throw new TenantIsolationError('Cannot perform database operations with trap tenant')
		}
	}

	/**
	 * Create a safe trap context as fallback
	 * @returns Trap context with TENANT set to 'trap'
	 */
	private createTrapContext(): ContextType {
		const trapContext: ContextType = {
			[TENANT]: TRAP,
			requestId: this.generateRequestId(),
			_isTrapContext: true,
			_createdAt: Date.now()
		}

		// Add safe accessors for trap context
		this.addSafeAccessors(trapContext)

		this.trackTrapContextUsage(trapContext)
		return trapContext
	}

	/**
	 * Track trap context usage for security monitoring
	 * @param context - Trap context that was created
	 */
	private trackTrapContextUsage(context: ContextType): void {
		console.warn('SECURITY: Trap context created', {
			requestId: context.requestId,
			timestamp: context._createdAt,
			caller: new Error().stack?.split('\n')[3]?.trim()
		})
	}

	// ---  Extended methods

	/**
	 * Set the context values
	 * @param tenant - tenant code
	 * @param [user] - CRM user or App user
	 * @param [timezone] - timezone of tenant
	 * @param [origin] - origin of request
	 */
	setValues(tenant: string, user?: User, timezone?: string, origin?: string) {
		this.tenant = tenant
		this.user = user
		this.timezone = timezone
		if (origin) this.origin = origin
	}

	/**
	 * Set the context with the token, stored in the context:
	 * 	tenant - tenant code
	 * 	user - username
	 * 	timezone - timezone of tenant
	 * 	exp - expiration of token
	 * @param accessToken - jwt token
	 * @param secretKey - jwt secret key
	 * @param options - allowExpired
	 * @returns - payload or error
	 */
	setWithToken(accessToken: string, secretKey: string, options: any = {}): Payload | Error {
		if (!accessToken) {
			return new TenantIsolationError('Authentication: failed to get token')
		}

		this.accessToken = accessToken
		let decodedJWT

		try {
			const jwt = new Jwt(secretKey)

			if (jwt.verify(accessToken)) {
				decodedJWT = jwt.decode(accessToken)
			}
			else {
				return new TenantIsolationError('Authentication: invalid token: ' + accessToken)
			}
		}
		catch (err: any) {
			return new TenantIsolationError(`Authentication error: ${err.message}`)
		}

		try {
			const { allowExpired } = options,
				payload: JwtPayload = (typeof decodedJWT.payload === 'string')
					? JSON.parse(decodedJWT.payload)
					: decodedJWT.payload,
				{ user, exp } = payload,
				{ code = '', timezone } = payload.tenant ?? {},
				tenant = code.toLowerCase(),
				result = { tenant, user, timezone, exp }

			if (!allowExpired && payload[EXPIRY]) {
				const now = Math.floor(Date.now() / 1000)

				if (payload[EXPIRY] <= now) {
					return new TenantIsolationError('Authentication: token has expired')
				}
			}
			if (!tenant) {
				return new TenantIsolationError('Authentication: missing Tenant Code')
			}

			this.setValues(tenant, user, timezone)
			return result
		}
		catch (err: any) {
			return new TenantIsolationError(`Token parsing error: ${err.message}`)
		}
	}

	bindEmitter(emitter: NodeJS.EventEmitter): void {
		const d = process.domain
		if (d && d._loopbackContext) {
			d.add(emitter)
		}
	}

	generateAccessToken(payload: any = {}, secret = PERKD_SECRET_KEY): string {
		if (!payload.tenant) {
			payload.tenant = {
				code: this.tenant,
				timezone: this.timezone
			}
		}
		const jwt = new Jwt(secret)
		return jwt.encode(payload)
	}

	generateRequestId(): string {
		return Date.now().toString(36) + Math.random().toString(36).slice(2)
	}

	// ---  Execution in Context

	async runInContext(context: ContextType, fn: () => any): Promise<any> {
		// Validate tenant exists using Symbol key
		if (process.env.CONTEXT_MODE === 'strict' && !context[TENANT]) {
			throw new TenantIsolationError('Missing tenant in context')
		}

		// Create enhanced context without circular references
		const enhancedContext = this.createEnhancedContext(context)

		try {
			if (process.env.CONTEXT_MODE === 'strict') {
				return this.als.run(enhancedContext, () => {
					enhancedContext[TENANT] = context[TENANT]
					return fn()
				})
			}

			// Legacy domain-based handling (will be deprecated)
			const d = this._createDomain(enhancedContext)
			enhancedContext._domain = d

			return await this.als.run(enhancedContext, () => d.run(() => {
				d._loopbackContext = enhancedContext
				return fn()
			}))
		}
		catch (err) {
			console.error('Error in runInContext:', err)
			throw err
		}
		finally {
			// Always release enhanced context to prevent memory leaks
			this.releaseContext(enhancedContext)
		}
	}

	/**
	 * Create enhanced context without circular references
	 */
	private createEnhancedContext(context: ContextType): ContextType {
		const enhancedContext: ContextType = {
			[TENANT]: context[TENANT],		// Use Symbol key
			...context,
			_isEnhanced: true
		}

		// Add safe accessors that don't create circular references
		this.addSafeAccessors(enhancedContext)

		return enhancedContext
	}

	async runAsTenant(tenant: string | Record<string, any>, fn: () => any, connectionManager?: ConnectionManager, options: Record<string, any> = {}): Promise<any> {
		if (!tenant) {
			throw new TenantIsolationError('runAsTenant: missing Tenant Code')
		}

		const tenantConfig = typeof tenant === 'string'
			? { id: tenant, ...options }
			: tenant
		const tenantCode = tenantConfig.id

		// CRITICAL: Prevent database operations with trap tenant
		if (connectionManager) {
			this.validateTenantForDatabaseOps(tenantCode)
		}

		const baseContext = this.context || {}
		const ctx: ContextType = {
			...baseContext,
			[TENANT]: tenantCode,
			tenantConfig: { ...baseContext.tenantConfig, ...tenantConfig }
		}

		// Store original context values to restore in case of error
		const originalContext = {
			tenant: this.tenant,
			accessToken: this.accessToken,
			user: this.user,
			timezone: this.timezone
		}

		let sessionTracker: SessionTracker | null = null

		try {
			return await this.runInContext(ctx, async () => {
				let session: Session | null = null

				try {
					if (connectionManager) {
						await connectionManager.ensureConnection(tenantCode)
						session = await connectionManager.startSession(tenantCode)
						
						// Track the session for cleanup
						if (session) {
							sessionTracker = {
								tenant: tenantCode,
								session,
								connectionManager,
								createdAt: Date.now()
							}
							this._activeSessions.set(session.id || `${tenantCode}-${Date.now()}`, sessionTracker)
						}
					}
					return await fn()
				}
				catch (err) {
					console.error('Error during tenant operation:', err)
					throw err
				}
				finally {
					// Enhanced session cleanup
					if (session && sessionTracker) {
						await this.cleanupSession(sessionTracker)
						this._activeSessions.delete(session.id || `${tenantCode}-${Date.now()}`)
					}
				}
			})
		}
		catch (err) {
			// Restore original context values on error
			try {
				this.tenant = originalContext.tenant
				this.accessToken = originalContext.accessToken
				this.user = originalContext.user
				this.timezone = originalContext.timezone
			} catch (restoreErr) {
				console.error('Error restoring original context:', restoreErr)
			}

			throw err
		}
	}

	/**
	 * Enhanced session cleanup with retries and timeout
	 */
	private async cleanupSession(tracker: SessionTracker): Promise<void> {
		const { session, connectionManager, tenant } = tracker
		const maxRetries = 3
		const timeoutMs = 5000

		for (let retry = 0; retry < maxRetries; retry++) {
			try {
				// Set a timeout for cleanup operations
				await Promise.race([
					this.performSessionCleanup(session, connectionManager, tenant),
					new Promise((_, reject) => 
						setTimeout(() => reject(new Error('Session cleanup timeout')), timeoutMs)
					)
				])
				
				console.debug(`Session cleaned up successfully for tenant ${tenant}`)
				return
			} catch (err) {
				console.error(`Session cleanup attempt ${retry + 1} failed for tenant ${tenant}:`, err)
				
				if (retry === maxRetries - 1) {
					console.error(`Failed to cleanup session for tenant ${tenant} after ${maxRetries} attempts`)
					// Force cleanup to prevent resource leaks
					await this.forceSessionCleanup(session)
				} else {
					// Wait before retry
					await new Promise(resolve => setTimeout(resolve, 100 * (retry + 1)))
				}
			}
		}
	}

	/**
	 * Perform the actual session cleanup
	 */
	private async performSessionCleanup(session: Session, connectionManager: ConnectionManager, tenant: string): Promise<void> {
		if (session.hasActiveTransaction?.()) {
			await session.abortTransaction?.()
		}
		
		await session.endSession?.()
		
		// Notify connection manager
		if (connectionManager.releaseSession) {
			await connectionManager.releaseSession(tenant, session)
		}
	}

	/**
	 * Force cleanup when normal cleanup fails
	 */
	private async forceSessionCleanup(session: Session): Promise<void> {
		try {
			// Try to force end the session
			if (session.endSession) {
				await session.endSession()
			}
		} catch (err) {
			console.error('Force cleanup also failed:', err)
		}
	}

	/**
	 * Get active session count for monitoring
	 */
	getActiveSessionCount(tenant?: string): number {
		if (tenant) {
			return Array.from(this._activeSessions.values())
				.filter(tracker => tracker.tenant === tenant).length
		}
		return this._activeSessions.size
	}

	/**
	 * Force cleanup all sessions for a tenant
	 */
	async forceCleanupTenant(tenant: string): Promise<number> {
		const tenantSessions = Array.from(this._activeSessions.entries())
			.filter(([_, tracker]) => tracker.tenant === tenant)

		const cleanupPromises = tenantSessions.map(async ([sessionId, tracker]) => {
			try {
				await this.cleanupSession(tracker)
				this._activeSessions.delete(sessionId)
				return true
			} catch (err) {
				console.error(`Failed to cleanup session ${sessionId}:`, err)
				return false
			}
		})

		const results = await Promise.allSettled(cleanupPromises)
		const successCount = results.filter(r => r.status === 'fulfilled' && r.value).length

		console.log(`Cleaned up ${successCount}/${tenantSessions.length} sessions for tenant ${tenant}`)
		return successCount
	}

	/**
	 * Execute with role privileges
	 * @param user
	 * @param role
	 * @param fn
	 * @param tenantCode
	 */
	async runWithPrivilege(user: User, role: string, fn: () => any, tenantCode?: string): Promise<any> {
		const { accessToken: oToken, tenant: oTenant } = this

		try {
			const payload = {
				user: { ...user, roles: [role] },
				tenant: { code: tenantCode || oTenant }
			},
				token = this.generateAccessToken(payload)

			if (tenantCode) {
				this.tenant = tenantCode
				// FIXME await cacheDataSource(app, code)
			}
			this.accessToken = token

			const res = await fn()
			return res
		}
		catch (err) {
			throw err instanceof Error ? err : new Error(String(err))
		}
		finally {
			this.accessToken = oToken
			if (tenantCode && oTenant) this.tenant = oTenant
		}
	}

	private _createDomain(context: ContextType): domain.Domain {
		const d = domain.create()
		d._loopbackContext = context
		this.activeDomains.set(d, true)
		d.on('error', (err) => this._handleDomainError(err, d))
		return d
	}

	private _handleDomainError(err: Error, d: domain.Domain): void {
		console.error('Domain error:', err)
		this.activeDomains.delete(d)
	}

	trackActiveContext(context: ContextType): void {
		// Keep track of contexts in a Set or Array
		if (!this._activeContextsRegistry) {
			this._activeContextsRegistry = new Set<ContextType>();
		}
		this._activeContextsRegistry.add(context);
	}

	releaseAllContexts(): void {
		// If we're tracking contexts separately
		if (this._activeContextsRegistry) {
			const contexts = Array.from(this._activeContextsRegistry);
			contexts.forEach(context => {
				this.releaseContext(context);
				this._activeContextsRegistry?.delete(context);
			});
		}

		console.info('All active contexts released');
	}
}

const Context = new MultitenantContext()
export default Context
