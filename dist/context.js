"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const tslib_1 = require("tslib");
const node_async_hooks_1 = require("node:async_hooks");
const node_domain_1 = tslib_1.__importDefault(require("node:domain"));
const utils_1 = require("@perkd/utils");
const errors_1 = require("@perkd/errors");
const types_1 = require("./types");
// environment variables
const { DISABLE_ASYNC_CONTEXT, PERKD_SECRET_KEY = 'perkd-secret-key' } = process.env;
const { Jwt } = utils_1.Security, ENGLISH = 'en', EXPIRY = 'exp'; // jwt
class MultitenantContext {
    static _instance;
    als;
    activeDomains;
    metrics;
    enabled;
    _activeContextsRegistry;
    static withContext(context, fn) {
        const instance = new MultitenantContext();
        return instance.runInContext(context, fn);
    }
    constructor() {
        if (MultitenantContext._instance) {
            return MultitenantContext._instance;
        }
        this.als = new node_async_hooks_1.AsyncLocalStorage();
        this.activeDomains = new WeakMap();
        this.metrics = {
            domainFallbacks: 0,
            trapFallbacks: 0,
            strictAccess: 0,
            total: 0
        };
        this.enabled = DISABLE_ASYNC_CONTEXT !== 'true';
        MultitenantContext._instance = Object.freeze(this);
    }
    // ---  loopback-context compatibility
    createContext(initialValue = {}) {
        const { requestId = this.generateRequestId() } = initialValue;
        return {
            ...initialValue,
            requestId,
            get: (key) => this.get(key),
            set: (key, value) => this.set(key, value),
            _domain: this._createDomain(initialValue)
        };
    }
    releaseContext(context) {
        if (!context)
            return;
        const contextId = context.requestId || 'unknown';
        const isTrap = this.isTrapContext(context);
        try {
            // Legacy domain cleanup (will be removed after trap context migration)
            if (context._domain) {
                context._domain.removeAllListeners();
                if (context._domain._loopbackContext === context) {
                    delete context._domain._loopbackContext;
                }
                this.activeDomains.delete(context._domain);
                delete context._domain;
            }
            // Clean up specific problematic function references that cause memory leaks
            // These are added by runInContext and create closures that capture 'this'
            if (context.get && typeof context.get === 'function') {
                delete context.get;
            }
            if (context.set && typeof context.set === 'function') {
                delete context.set;
            }
            // Clean up trap context specific properties
            if (isTrap) {
                delete context._isTrapContext;
                delete context._createdAt;
                // For trap contexts, clean up all properties except tenant (they're all generated)
                Object.keys(context).forEach(key => {
                    if (key !== types_1.TENANT.toString() && key !== '_released') {
                        try {
                            delete context[key];
                        }
                        catch (err) {
                            console.debug(`Failed to delete trap property ${key}:`, err instanceof Error ? err.message : String(err));
                        }
                    }
                });
            }
            else {
                // For regular contexts, only clean up nested objects that might have circular references
                const visited = new WeakSet();
                Object.keys(context).forEach(key => {
                    try {
                        if (context[key] && typeof context[key] === 'object' && key !== types_1.TENANT.toString()) {
                            this.cleanNestedReferences(context[key], visited);
                        }
                    }
                    catch (err) {
                        console.debug(`Failed to clean nested property ${key}:`, err instanceof Error ? err.message : String(err));
                    }
                });
            }
            console.debug(`Released ${isTrap ? 'trap' : 'tenant'} context ${contextId}`);
        }
        catch (err) {
            console.error('Context cleanup error:', err);
        }
        finally {
            // Always mark as released, even if cleanup fails
            try {
                Object.defineProperty(context, '_released', {
                    value: true,
                    configurable: false
                });
            }
            catch (err) {
                // Fallback if defineProperty fails
                context._released = true;
            }
        }
    }
    cleanNestedReferences(obj, visited = new WeakSet()) {
        if (!obj || typeof obj !== 'object' || visited.has(obj))
            return;
        visited.add(obj);
        Object.keys(obj).forEach(key => {
            try {
                if (typeof obj[key] === 'function') {
                    delete obj[key];
                }
                else if (obj[key] && typeof obj[key] === 'object' && !visited.has(obj[key])) {
                    this.cleanNestedReferences(obj[key], visited);
                }
            }
            catch (err) {
                // Continue cleanup even if individual property fails
                console.debug(`Failed to clean nested property ${key}:`, err instanceof Error ? err.message : String(err));
            }
        });
    }
    getCurrentContext() {
        const { metrics } = this;
        const ctx = this.als.getStore();
        metrics.total++;
        if (ctx) {
            metrics.strictAccess++;
            return ctx;
        }
        // Replace dangerous domain fallback with safe trap context
        if (process.env.CONTEXT_MODE === 'strict') {
            throw new errors_1.TenantIsolationError('No tenant context in strict mode');
        }
        metrics.trapFallbacks++;
        console.warn(`Trap context fallback (${metrics.trapFallbacks}): ${metrics.trapFallbacks / metrics.total * 100}%`);
        return this.createTrapContext();
    }
    get(key) {
        return this.context?.[key];
    }
    set(key, value) {
        if (this.context) {
            this.context[key] = value;
            return value;
        }
    }
    // ---  accessors
    get context() {
        return this.getCurrentContext();
    }
    get tenant() {
        return this.context?.[types_1.TENANT] || types_1.TRAP;
    }
    set tenant(code) {
        if (this.context) {
            this.context[types_1.TENANT] = code.toLowerCase();
        }
    }
    get accessToken() {
        return this.context?.[types_1.ACCESS_TOKEN] || '';
    }
    set accessToken(token) {
        if (this.context) {
            this.context[types_1.ACCESS_TOKEN] = token;
        }
    }
    get timezone() {
        return this.context?.[types_1.TIMEZONE];
    }
    set timezone(timezone) {
        if (this.context) {
            this.context[types_1.TIMEZONE] = timezone;
        }
    }
    get user() {
        return this.context?.[types_1.USER];
    }
    set user(user) {
        if (this.context) {
            this.context[types_1.USER] = user;
        }
    }
    get origin() {
        return this.context?.[types_1.ORIGIN];
    }
    set origin(origin) {
        if (this.context) {
            this.context[types_1.ORIGIN] = origin;
        }
    }
    get idempotencyKey() {
        return this.get(types_1.IDEMPOTENCY_KEY) || (0, utils_1.shortId)();
    }
    set idempotencyKey(value) {
        if (this.context) {
            this.context[types_1.IDEMPOTENCY_KEY] = value;
        }
    }
    //  Perkd App specific
    get appContext() {
        const { location: loc, user, cardProfile: card, installation } = this, { spot } = loc ?? {}, { id: userId, staffId, username: userName } = user ?? {}, staff = staffId ? { id: staffId, userId, userName } : undefined, { id, type, name } = spot ?? {}, location = { id, type, name };
        return { staff, user, card, location, installation };
    }
    get accountId() {
        const { accountId = null } = this.user ?? {};
        return accountId;
    }
    /**
     * Get installation & user from Perkd App
     * @return	{Object} { ...install, ...user }
     * 1. install - decoded JWT perkd-install header. (see installation/docs/perkd-install.json)
     * 2. user - user object in decoded JWT Authorization header. (see installation/docs/Authorization.json)
     */
    get installation() {
        const { user } = this, install = this.get(types_1.INSTALL), { id: userId } = user ?? {}, { personId, accountId } = user ?? {};
        if (!install)
            return {};
        if (!install.id) {
            const err = new Error('header_missing_install');
            console.error('getInstall', { install, userId, personId, accountId, err });
        }
        // CRM: inject userId (personId & accountId N.A) (placeId already in install)
        // Perkd: inject personId, accountId (userId N.A)
        return { ...install, userId, personId, accountId };
    }
    set installation(installation) {
        if (this.context) {
            this.context[types_1.INSTALL] = installation;
        }
    }
    get cardProfile() {
        return this.get(types_1.CARD) || {};
    }
    set cardProfile(card) {
        if (this.context) {
            this.context[types_1.CARD] = card;
        }
    }
    get location() {
        return this.get(types_1.LOCATION) || {};
    }
    set location(location) {
        if (this.context) {
            this.context[types_1.LOCATION] = location;
        }
    }
    get language() {
        return this.get(types_1.LANGUAGE) || ENGLISH;
    }
    set language(language) {
        if (this.context) {
            this.context[types_1.LANGUAGE] = language;
        }
    }
    // ---  Trap Context Utilities
    /**
     * Check if a context is a trap context
     * @param context - Context to check (defaults to current context)
     * @returns true if context is a trap context
     */
    isTrapContext(context) {
        const ctx = context || this.context;
        return ctx?._isTrapContext === true || ctx?.[types_1.TENANT] === types_1.TRAP;
    }
    /**
     * Validate tenant for database operations
     * @param tenantCode - Tenant code to validate
     * @throws TenantIsolationError if tenant is trap
     */
    validateTenantForDatabaseOps(tenantCode) {
        if (tenantCode === types_1.TRAP) {
            throw new errors_1.TenantIsolationError('Cannot perform database operations with trap tenant');
        }
    }
    /**
     * Create a safe trap context as fallback
     * @returns Trap context with TENANT set to 'trap'
     */
    createTrapContext() {
        const trapContext = {
            [types_1.TENANT]: types_1.TRAP,
            requestId: this.generateRequestId(),
            _isTrapContext: true,
            _createdAt: Date.now(),
            get: (key) => this.get(key),
            set: (key, value) => this.set(key, value)
        };
        this.trackTrapContextUsage(trapContext);
        return trapContext;
    }
    /**
     * Track trap context usage for security monitoring
     * @param context - Trap context that was created
     */
    trackTrapContextUsage(context) {
        console.warn('SECURITY: Trap context created', {
            requestId: context.requestId,
            timestamp: context._createdAt,
            caller: new Error().stack?.split('\n')[3]?.trim()
        });
    }
    // ---  Extended methods
    /**
     * Set the context values
     * @param tenant - tenant code
     * @param [user] - CRM user or App user
     * @param [timezone] - timezone of tenant
     * @param [origin] - origin of request
     */
    setValues(tenant, user, timezone, origin) {
        this.tenant = tenant;
        this.user = user;
        this.timezone = timezone;
        if (origin)
            this.origin = origin;
    }
    /**
     * Set the context with the token, stored in the context:
     * 	tenant - tenant code
     * 	user - username
     * 	timezone - timezone of tenant
     * 	exp - expiration of token
     * @param accessToken - jwt token
     * @param secretKey - jwt secret key
     * @param options - allowExpired
     * @returns - payload or error
     */
    setWithToken(accessToken, secretKey, options = {}) {
        if (!accessToken) {
            return new errors_1.TenantIsolationError('Authentication: failed to get token');
        }
        this.accessToken = accessToken;
        let decodedJWT;
        try {
            const jwt = new Jwt(secretKey);
            if (jwt.verify(accessToken)) {
                decodedJWT = jwt.decode(accessToken);
            }
            else {
                return new errors_1.TenantIsolationError('Authentication: invalid token: ' + accessToken);
            }
        }
        catch (err) {
            return new errors_1.TenantIsolationError(`Authentication error: ${err.message}`);
        }
        try {
            const { allowExpired } = options, payload = (typeof decodedJWT.payload === 'string')
                ? JSON.parse(decodedJWT.payload)
                : decodedJWT.payload, { user, exp } = payload, { code = '', timezone } = payload.tenant ?? {}, tenant = code.toLowerCase(), result = { tenant, user, timezone, exp };
            if (!allowExpired && payload[EXPIRY]) {
                const now = Math.floor(Date.now() / 1000);
                if (payload[EXPIRY] <= now) {
                    return new errors_1.TenantIsolationError('Authentication: token has expired');
                }
            }
            if (!tenant) {
                return new errors_1.TenantIsolationError('Authentication: missing Tenant Code');
            }
            this.setValues(tenant, user, timezone);
            return result;
        }
        catch (err) {
            return new errors_1.TenantIsolationError(`Token parsing error: ${err.message}`);
        }
    }
    bindEmitter(emitter) {
        const d = process.domain;
        if (d && d._loopbackContext) {
            d.add(emitter);
        }
    }
    generateAccessToken(payload = {}, secret = PERKD_SECRET_KEY) {
        if (!payload.tenant) {
            payload.tenant = {
                code: this.tenant,
                timezone: this.timezone
            };
        }
        const jwt = new Jwt(secret);
        return jwt.encode(payload);
    }
    generateRequestId() {
        return Date.now().toString(36) + Math.random().toString(36).slice(2);
    }
    // ---  Execution in Context
    async runInContext(context, fn) {
        // Validate tenant exists using Symbol key
        if (process.env.CONTEXT_MODE === 'strict' && !context[types_1.TENANT]) {
            throw new errors_1.TenantIsolationError('Missing tenant in context');
        }
        const enhancedContext = {
            [types_1.TENANT]: context[types_1.TENANT], // Use Symbol key
            ...context,
            get: (key) => this.get(key),
            set: (key, value) => this.set(key, value)
        };
        try {
            if (process.env.CONTEXT_MODE === 'strict') {
                return this.als.run(enhancedContext, () => {
                    enhancedContext[types_1.TENANT] = context[types_1.TENANT];
                    return fn();
                });
            }
            // Legacy domain-based handling
            const d = this._createDomain(enhancedContext);
            enhancedContext._domain = d;
            return await this.als.run(enhancedContext, () => d.run(() => {
                d._loopbackContext = enhancedContext;
                return fn();
            }));
        }
        catch (err) {
            console.error('Error in runInContext:', err);
            throw err;
        }
        finally {
            // Always release enhanced context to prevent memory leaks
            // NOTE: Do NOT release the original context - caller may still need it
            this.releaseContext(enhancedContext);
            // Additional cleanup for enhanced context to break circular references
            // (this is redundant with releaseContext but kept for safety)
            try {
                delete enhancedContext.get;
                delete enhancedContext.set;
            }
            catch (err) {
                // Ignore cleanup errors
            }
        }
    }
    async runAsTenant(tenant, fn, connectionManager, options = {}) {
        if (!tenant) {
            throw new errors_1.TenantIsolationError('runAsTenant: missing Tenant Code');
        }
        const tenantConfig = typeof tenant === 'string'
            ? { id: tenant, ...options }
            : tenant;
        const tenantCode = tenantConfig.id;
        // CRITICAL: Prevent database operations with trap tenant
        if (connectionManager) {
            this.validateTenantForDatabaseOps(tenantCode);
        }
        const baseContext = this.context || {};
        const ctx = {
            ...baseContext,
            [types_1.TENANT]: tenantCode,
            tenantConfig: { ...baseContext.tenantConfig, ...tenantConfig }
        };
        // Store original context values to restore in case of error
        const originalContext = {
            tenant: this.tenant,
            accessToken: this.accessToken,
            user: this.user,
            timezone: this.timezone
        };
        try {
            return await this.runInContext(ctx, async () => {
                let session = null;
                try {
                    if (connectionManager) {
                        await connectionManager.ensureConnection(tenantCode);
                        session = await connectionManager.startSession(tenantCode);
                    }
                    return await fn();
                }
                catch (err) {
                    console.error('Error during tenant operation:', err);
                    throw err;
                }
                finally {
                    if (session) {
                        try {
                            await connectionManager.withTransaction(tenantCode, async () => {
                                if (session.hasActiveTransaction?.()) {
                                    await session.abortTransaction?.();
                                }
                            });
                        }
                        catch (err) {
                            console.error('Error cleaning up session:', err);
                        }
                    }
                }
            });
        }
        catch (err) {
            // Restore original context values on error
            try {
                this.tenant = originalContext.tenant;
                this.accessToken = originalContext.accessToken;
                this.user = originalContext.user;
                this.timezone = originalContext.timezone;
            }
            catch (restoreErr) {
                console.error('Error restoring original context:', restoreErr);
            }
            throw err;
        }
    }
    /**
     * Execute with role privileges
     * @param user
     * @param role
     * @param fn
     * @param tenantCode
     */
    async runWithPrivilege(user, role, fn, tenantCode) {
        const { accessToken: oToken, tenant: oTenant } = this;
        try {
            const payload = {
                user: { ...user, roles: [role] },
                tenant: { code: tenantCode || oTenant }
            }, token = this.generateAccessToken(payload);
            if (tenantCode) {
                this.tenant = tenantCode;
                // FIXME await cacheDataSource(app, code)
            }
            this.accessToken = token;
            const res = await fn();
            return res;
        }
        catch (err) {
            throw err instanceof Error ? err : new Error(String(err));
        }
        finally {
            this.accessToken = oToken;
            if (tenantCode && oTenant)
                this.tenant = oTenant;
        }
    }
    _createDomain(context) {
        const d = node_domain_1.default.create();
        d._loopbackContext = context;
        this.activeDomains.set(d, true);
        d.on('error', (err) => this._handleDomainError(err, d));
        return d;
    }
    _handleDomainError(err, d) {
        console.error('Domain error:', err);
        this.activeDomains.delete(d);
    }
    trackActiveContext(context) {
        // Keep track of contexts in a Set or Array
        if (!this._activeContextsRegistry) {
            this._activeContextsRegistry = new Set();
        }
        this._activeContextsRegistry.add(context);
    }
    releaseAllContexts() {
        // If we're tracking contexts separately
        if (this._activeContextsRegistry) {
            const contexts = Array.from(this._activeContextsRegistry);
            contexts.forEach(context => {
                this.releaseContext(context);
                this._activeContextsRegistry?.delete(context);
            });
        }
        console.info('All active contexts released');
    }
}
const Context = new MultitenantContext();
exports.default = Context;
//# sourceMappingURL=context.js.map